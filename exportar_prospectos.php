<?php
// Script simplificado para exportar prospectos a CSV
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);
ini_set('error_log', __DIR__ . '/logs/export_errors.log');

// Log para debugging
error_log("=== EXPORTAR_PROSPECTOS.PHP INICIADO ===");

// Iniciar sesión
session_start();

// Prevenir caché del navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// Incluir archivos necesarios
require_once 'cache_utils.php';
require_once 'con_db.php';

// Aplicar headers anti-caché
no_cache_headers();

// Obtener parámetros de filtros
$filtro_ejecutivo = isset($_GET['ejecutivo']) ? $_GET['ejecutivo'] : 'todos';
$filtro_periodo = isset($_GET['periodo']) ? $_GET['periodo'] : 'año';
$filtro_fecha_inicio = isset($_GET['fecha_inicio']) ? $_GET['fecha_inicio'] : date('Y-01-01');
$filtro_fecha_fin = isset($_GET['fecha_fin']) ? $_GET['fecha_fin'] : date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

// Obtener la estructura de la tabla
$tabla_info_query = "DESCRIBE tb_inteletgroup_prospectos";
$tabla_info_result = $mysqli->query($tabla_info_query);
$columnas = [];
while ($row = $tabla_info_result->fetch_assoc()) {
    $columnas[] = $row['Field'];
}
error_log("Columnas disponibles en tb_inteletgroup_prospectos: " . implode(", ", $columnas));

// Crear una consulta simple usando solo columnas existentes según el log
$query = "
    SELECT 
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.direccion_comercial, 
        p.usuario_id, p.fecha_registro, p.estado,
        COALESCE(u.nombre_usuario, p.nombre_ejecutivo) as ejecutivo_nombre_usuario
    FROM 
        tb_inteletgroup_prospectos p
    LEFT JOIN 
        tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE 
        $where_clause
    ORDER BY 
        p.fecha_registro DESC";

// Ejecutar la consulta
$stmt = $mysqli->prepare($query);
if (!$stmt) {
    error_log("ERROR: Error preparando consulta: " . $mysqli->error);
    error_log("Query: " . $query);
    die("Error preparando consulta: " . $mysqli->error);
}

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

if (!$stmt->execute()) {
    error_log("ERROR: Error ejecutando consulta: " . $stmt->error);
    die("Error ejecutando consulta: " . $stmt->error);
}

$result = $stmt->get_result();
$prospectos = [];

while ($row = $result->fetch_assoc()) {
    // Añadir información sobre documentos
    $doc_query = "
        SELECT 
            COUNT(DISTINCT d.id) as total_documentos,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
        FROM 
            tb_inteletgroup_prospectos p
        LEFT JOIN 
            tb_inteletgroup_tipos_documento td ON
            CONVERT(td.tipo_persona USING utf8mb4) = CONVERT(p.tipo_persona USING utf8mb4) COLLATE utf8mb4_spanish_ci
            OR td.tipo_persona = 'Ambos'
            AND td.estado = 'Activo'
        LEFT JOIN 
            tb_inteletgroup_documentos d ON
            p.id = d.prospecto_id
            AND d.tipo_documento_id = td.id
            AND d.estado = 'Activo'
        WHERE 
            p.id = ?";
    
    $doc_stmt = $mysqli->prepare($doc_query);
    $doc_stmt->bind_param("i", $row['id']);
    $doc_stmt->execute();
    $doc_result = $doc_stmt->get_result();
    $doc_row = $doc_result->fetch_assoc();
    $doc_stmt->close();
    
    $total_documentos = $doc_row['total_documentos'] ?? 0;
    $obligatorios_completados = $doc_row['obligatorios_completados'] ?? 0;
    $total_obligatorios = $doc_row['total_obligatorios'] ?? 0;
    $porcentaje_completado = $total_obligatorios > 0 ? 
        round(($obligatorios_completados / $total_obligatorios) * 100) : 0;
    
    // Combinar datos de prospectos y documentos - usando solo columnas disponibles
    $prospectos[] = [
        'ID' => $row['id'],
        'Tipo de Persona' => $row['tipo_persona'],
        'RUT' => $row['rut_cliente'],
        'Razón Social' => $row['razon_social'],
        'Rubro' => $row['rubro'],
        'Email' => $row['email'],
        'Teléfono Celular' => $row['telefono_celular'],
        'Dirección Comercial' => $row['direccion_comercial'],
        'Fecha Registro' => $row['fecha_registro'],
        'Estado' => $row['estado'],
        'Ejecutivo' => $row['ejecutivo_nombre_usuario'],
        'Total Documentos' => $total_documentos,
        'Documentos Obligatorios Completados' => $obligatorios_completados,
        'Total Documentos Obligatorios' => $total_obligatorios,
        'Porcentaje Completado' => $porcentaje_completado . '%'
    ];
}

$stmt->close();
// No cerrar $mysqli aquí, se maneja con register_shutdown_function

// Verificar si hay datos
if (empty($prospectos)) {
    error_log("ADVERTENCIA: No hay prospectos para exportar");
    die("No hay datos para exportar con los filtros seleccionados");
}

// Nombre del archivo para la descarga
$fecha_actual = date('Y-m-d');
$nombre_archivo = "prospectos_inteletgroup_{$fecha_actual}.csv";

// Configurar encabezados para descarga de CSV
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');

// Crear el recurso de salida para escribir el CSV
$output = fopen('php://output', 'w');

// Configurar para que funcione con caracteres especiales (acentos, etc)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Escribir encabezados de las columnas
fputcsv($output, array_keys($prospectos[0]));

// Escribir datos
foreach ($prospectos as $prospecto) {
    fputcsv($output, $prospecto);
}

fclose($output);

// Suprimir el cierre automático de la conexión para este script
if (isset($mysqli)) {
    global $mysqli;
    $mysqli = null; // Evitar que el shutdown function intente cerrar una conexión ya cerrada
}

exit;
