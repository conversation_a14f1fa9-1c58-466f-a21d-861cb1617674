[08-Jul-2025 03:25:09 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:25:09 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:25:09 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:25:09 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:25:09 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:25:09 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:25:09 Europe/Berlin] Parámetros GET recibidos: Array
(
    [ejecutivo] => todos
    [periodo] => año
    [fecha_inicio] => 
    [fecha_fin] => 
    [ajax] => 1
    [_t] => 1751937909397
)

[08-Jul-2025 03:25:09 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:25:09 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: Sí
[08-Jul-2025 03:25:09 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:25:09 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:25:09 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:25:10 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:25:10 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:25:10 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:25:10 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:25:10 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:25:10 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:25:10 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:25:10 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:25:10 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:25:10 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:25:10 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:25:10 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:25:10 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:25:46 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:25:46 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:25:46 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:25:46 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:25:46 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:25:46 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:25:46 Europe/Berlin] Parámetros GET recibidos: Array
(
    [ejecutivo] => todos
    [periodo] => año
    [fecha_inicio] => 
    [fecha_fin] => 
    [ajax] => 1
    [_t] => 1751937946643
)

[08-Jul-2025 03:25:46 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:25:46 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: Sí
[08-Jul-2025 03:25:46 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:25:46 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:25:46 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:25:47 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:25:47 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:25:47 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:25:47 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:25:47 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:25:47 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:25:47 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:25:47 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:25:47 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:25:47 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:25:47 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:25:47 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:25:47 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:31:36 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:31:36 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:31:36 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:31:36 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:31:36 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:31:36 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:31:36 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:31:36 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:31:36 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:31:36 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:31:36 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:31:36 Europe/Berlin] Parámetros GET recibidos: Array
(
    [ejecutivo] => todos
    [periodo] => año
    [fecha_inicio] => 
    [fecha_fin] => 
    [ajax] => 1
    [_t] => 1751938296045
)

[08-Jul-2025 03:31:36 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:31:36 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:31:36 Europe/Berlin] Parámetros GET recibidos: Array
(
)

[08-Jul-2025 03:31:36 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:31:36 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: Sí
[08-Jul-2025 03:31:36 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:31:36 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:31:36 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:31:36 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:31:36 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:31:36 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:31:36 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:31:36 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:31:36 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:31:36 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:31:36 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:31:36 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:31:36 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:31:36 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:31:37 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:31:37 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:31:37 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: No
[08-Jul-2025 03:31:37 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:31:37 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:31:37 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:31:38 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:31:38 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:31:38 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:31:38 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:31:38 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:31:38 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:31:38 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:31:38 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:31:38 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:31:38 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:31:38 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:31:38 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:31:38 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:41:00 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:41:00 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:41:00 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:41:00 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:41:00 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:41:00 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:41:00 Europe/Berlin] Parámetros GET recibidos: Array
(
)

[08-Jul-2025 03:41:00 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:41:00 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: No
[08-Jul-2025 03:41:00 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:41:00 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:41:00 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:41:01 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:41:01 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:41:01 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:41:01 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:41:01 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:41:01 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:41:01 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:41:01 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:41:01 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:41:01 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:41:01 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:41:01 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:41:01 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:41:17 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:41:17 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:41:17 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:41:17 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:41:17 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:41:17 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:41:17 Europe/Berlin] Parámetros GET recibidos: Array
(
    [ejecutivo] => todos
    [periodo] => año
    [fecha_inicio] => 
    [fecha_fin] => 
    [ajax] => 1
    [_t] => 1751938877638
)

[08-Jul-2025 03:41:17 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:41:17 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: Sí
[08-Jul-2025 03:41:17 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:41:17 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:41:17 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:41:18 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:41:18 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:41:18 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:41:18 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:41:18 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:41:18 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:41:18 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:41:18 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:41:18 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:41:18 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:41:18 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:41:18 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:41:18 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:41:39 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:41:39 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:41:39 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:41:39 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:41:39 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:41:39 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:41:39 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:41:39 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:41:39 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:41:39 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:41:39 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:41:39 Europe/Berlin] Parámetros GET recibidos: Array
(
)

[08-Jul-2025 03:41:39 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:41:39 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:41:39 Europe/Berlin] Parámetros GET recibidos: Array
(
    [ejecutivo] => todos
    [periodo] => año
    [fecha_inicio] => 
    [fecha_fin] => 
    [ajax] => 1
    [_t] => 1751938899905
)

[08-Jul-2025 03:41:39 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:41:39 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: No
[08-Jul-2025 03:41:39 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:41:39 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:41:39 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:41:40 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:41:40 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:41:40 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:41:40 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:41:40 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:41:40 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:41:40 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:41:40 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:41:40 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:41:40 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:41:40 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:41:40 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:41:40 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:41:41 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: Sí
[08-Jul-2025 03:41:41 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:41:41 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:41:41 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:41:41 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:41:41 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:41:41 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:41:41 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:41:41 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:41:41 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:41:41 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:41:41 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:41:41 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:41:41 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:41:41 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:41:42 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:41:42 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:42:43 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:42:43 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:42:43 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:42:43 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:42:43 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:42:43 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:42:43 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:42:43 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:42:43 Europe/Berlin] Parámetros GET recibidos: Array
(
)

[08-Jul-2025 03:42:43 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:42:43 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:42:43 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:42:43 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:42:43 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: No
[08-Jul-2025 03:42:43 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:42:43 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:42:43 Europe/Berlin] Parámetros GET recibidos: Array
(
    [ejecutivo] => todos
    [periodo] => año
    [fecha_inicio] => 
    [fecha_fin] => 
    [ajax] => 1
    [_t] => 1751938963467
)

[08-Jul-2025 03:42:43 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:42:43 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:42:43 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:42:44 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:42:44 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:42:44 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:42:44 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:42:44 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:42:44 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:42:44 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:42:44 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:42:44 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:42:44 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:42:44 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:42:44 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:42:44 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:42:44 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: Sí
[08-Jul-2025 03:42:44 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:42:44 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:42:44 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:42:45 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:42:45 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:42:45 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:42:45 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:42:45 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:42:45 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:42:45 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:42:45 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:42:45 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:42:45 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:42:45 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:42:45 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:42:45 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 03:54:13 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 03:54:13 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 03:54:13 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 03:54:13 Europe/Berlin] Sesión activa: No
[08-Jul-2025 03:54:13 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 03:54:13 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 03:54:13 Europe/Berlin] Parámetros GET recibidos: Array
(
    [ejecutivo] => todos
    [periodo] => año
    [fecha_inicio] => 
    [fecha_fin] => 
    [ajax] => 1
    [_t] => 1751939653331
)

[08-Jul-2025 03:54:13 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 03:54:13 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: Sí
[08-Jul-2025 03:54:13 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 03:54:13 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 03:54:13 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 03:54:14 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 03:54:14 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 03:54:14 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 03:54:14 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 03:54:14 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 03:54:14 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 03:54:14 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 03:54:14 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 03:54:14 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 03:54:14 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 03:54:14 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 03:54:14 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 03:54:14 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 04:04:07 Europe/Berlin] === EXPORTAR_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:04:08 Europe/Berlin] Columnas disponibles en tb_inteletgroup_prospectos: id, usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro, direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta, numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta, competencia_actual, tipo_persona, documentos_adjuntos, fecha_registro, fecha_actualizacion, estado
[08-Jul-2025 04:04:09 Europe/Berlin] PHP Fatal error:  Uncaught mysqli_sql_exception: Unknown column 'p.direccion' in 'field list' in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php:103
Stack trace:
#0 C:\xampp\htdocs\intranet\dist\exportar_prospectos.php(103): mysqli->prepare('\n    SELECT \n  ...')
#1 {main}
  thrown in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php on line 103
[08-Jul-2025 04:04:20 Europe/Berlin] === EXPORTAR_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:04:21 Europe/Berlin] Columnas disponibles en tb_inteletgroup_prospectos: id, usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro, direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta, numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta, competencia_actual, tipo_persona, documentos_adjuntos, fecha_registro, fecha_actualizacion, estado
[08-Jul-2025 04:04:22 Europe/Berlin] PHP Fatal error:  Uncaught mysqli_sql_exception: Unknown column 'p.direccion' in 'field list' in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php:103
Stack trace:
#0 C:\xampp\htdocs\intranet\dist\exportar_prospectos.php(103): mysqli->prepare('\n    SELECT \n  ...')
#1 {main}
  thrown in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php on line 103
[08-Jul-2025 04:04:47 Europe/Berlin] === EXPORTAR_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:04:47 Europe/Berlin] === EXPORTAR_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:04:48 Europe/Berlin] Columnas disponibles en tb_inteletgroup_prospectos: id, usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro, direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta, numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta, competencia_actual, tipo_persona, documentos_adjuntos, fecha_registro, fecha_actualizacion, estado
[08-Jul-2025 04:04:48 Europe/Berlin] PHP Fatal error:  Uncaught mysqli_sql_exception: Unknown column 'p.direccion' in 'field list' in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php:103
Stack trace:
#0 C:\xampp\htdocs\intranet\dist\exportar_prospectos.php(103): mysqli->prepare('\n    SELECT \n  ...')
#1 {main}
  thrown in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php on line 103
[08-Jul-2025 04:04:49 Europe/Berlin] Columnas disponibles en tb_inteletgroup_prospectos: id, usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro, direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta, numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta, competencia_actual, tipo_persona, documentos_adjuntos, fecha_registro, fecha_actualizacion, estado
[08-Jul-2025 04:04:50 Europe/Berlin] PHP Fatal error:  Uncaught mysqli_sql_exception: Unknown column 'p.direccion' in 'field list' in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php:103
Stack trace:
#0 C:\xampp\htdocs\intranet\dist\exportar_prospectos.php(103): mysqli->prepare('\n    SELECT \n  ...')
#1 {main}
  thrown in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php on line 103
[08-Jul-2025 04:16:01 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 04:16:01 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 04:16:01 Europe/Berlin] === EXPORTAR_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:16:01 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 04:16:01 Europe/Berlin] Sesión activa: No
[08-Jul-2025 04:16:01 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 04:16:01 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 04:16:01 Europe/Berlin] Parámetros GET recibidos: Array
(
    [ejecutivo] => todos
    [periodo] => año
    [fecha_inicio] => 
    [fecha_fin] => 
    [ajax] => 1
    [_t] => 1751940961024
)

[08-Jul-2025 04:16:01 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:16:02 Europe/Berlin] Columnas disponibles en tb_inteletgroup_prospectos: id, usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro, direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta, numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta, competencia_actual, tipo_persona, documentos_adjuntos, fecha_registro, fecha_actualizacion, estado
[08-Jul-2025 04:16:02 Europe/Berlin] PHP Fatal error:  Uncaught mysqli_sql_exception: Illegal mix of collations (utf8mb4_spanish_ci,IMPLICIT) and (utf8mb4_0900_ai_ci,IMPLICIT) for operation '=' in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php:141
Stack trace:
#0 C:\xampp\htdocs\intranet\dist\exportar_prospectos.php(141): mysqli->prepare('\n        SELECT...')
#1 {main}
  thrown in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php on line 141
[08-Jul-2025 04:16:02 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: Sí
[08-Jul-2025 04:16:02 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 04:16:02 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 04:16:02 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 04:16:04 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 04:16:04 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 04:16:04 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 04:16:04 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 04:16:04 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 04:16:04 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 04:16:04 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 04:16:04 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 04:16:04 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 04:16:04 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 04:16:04 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 04:16:04 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 04:16:04 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 04:18:40 Europe/Berlin] === DETALLADO: Iniciando script de exportación ===

[08-Jul-2025 04:18:40 Europe/Berlin] PHP Version: 8.2.12
[08-Jul-2025 04:18:40 Europe/Berlin] Server info: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
[08-Jul-2025 04:18:40 Europe/Berlin] Sesión activa: No
[08-Jul-2025 04:18:40 Europe/Berlin] PHP Warning:  Undefined global variable $_SESSION in C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php on line 17
[08-Jul-2025 04:18:40 Europe/Berlin] Variables de sesión: 
[08-Jul-2025 04:18:40 Europe/Berlin] === EXPORTAR_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:18:40 Europe/Berlin] Parámetros GET recibidos: Array
(
    [ejecutivo] => todos
    [periodo] => año
    [fecha_inicio] => 
    [fecha_fin] => 
    [ajax] => 1
    [_t] => 1751941120936
)

[08-Jul-2025 04:18:40 Europe/Berlin] === INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:18:42 Europe/Berlin] Columnas disponibles en tb_inteletgroup_prospectos: id, usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro, direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta, numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta, competencia_actual, tipo_persona, documentos_adjuntos, fecha_registro, fecha_actualizacion, estado
[08-Jul-2025 04:18:43 Europe/Berlin] PHP Fatal error:  Uncaught mysqli_sql_exception: Illegal mix of collations (utf8mb4_spanish_ci,IMPLICIT) and (utf8mb4_0900_ai_ci,IMPLICIT) for operation '=' in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php:141
Stack trace:
#0 C:\xampp\htdocs\intranet\dist\exportar_prospectos.php(141): mysqli->prepare('\n        SELECT...')
#1 {main}
  thrown in C:\xampp\htdocs\intranet\dist\exportar_prospectos.php on line 141
[08-Jul-2025 04:18:43 Europe/Berlin] DEBUG: Verificando autenticación. Es solicitud AJAX: Sí
[08-Jul-2025 04:18:43 Europe/Berlin] Incluyendo archivos necesarios
[08-Jul-2025 04:18:43 Europe/Berlin] Archivo cache_utils.php incluido correctamente
[08-Jul-2025 04:18:43 Europe/Berlin] Intentando incluir con_db.php
[08-Jul-2025 04:18:44 Europe/Berlin] Archivo con_db.php incluido correctamente
[08-Jul-2025 04:18:44 Europe/Berlin] DEBUG: Conexión a BD establecida correctamente
[08-Jul-2025 04:18:44 Europe/Berlin] Versión MySQL: 8.0.42-cll-lve
[08-Jul-2025 04:18:44 Europe/Berlin] Versión del protocolo: 10
[08-Jul-2025 04:18:44 Europe/Berlin] Set de caracteres: utf8mb4
[08-Jul-2025 04:18:44 Europe/Berlin] INFO: Exportando prospectos con filtros - Ejecutivo: todos, Periodo: año
[08-Jul-2025 04:18:44 Europe/Berlin] INFO: Fechas - Inicio: 2025-01-01, Fin: 2025-12-31
[08-Jul-2025 04:18:44 Europe/Berlin] INFO: WHERE clause: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[08-Jul-2025 04:18:44 Europe/Berlin] Preparando consulta principal: 
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
[08-Jul-2025 04:18:44 Europe/Berlin] Parámetros: Array
(
    [0] => 2025-01-01
    [1] => 2025-12-31
)

[08-Jul-2025 04:18:44 Europe/Berlin] Tipos de parámetros: ss
[08-Jul-2025 04:18:44 Europe/Berlin] EXCEPCIÓN CAPTURADA en la consulta principal: Unknown column 'p.telefono_fijo' in 'field list'
[08-Jul-2025 04:18:44 Europe/Berlin] Traza: #0 C:\xampp\htdocs\intranet\dist\inteletgroup_export_prospectos.php(232): mysqli->prepare('\n    SELECT\n   ...')
#1 {main}
[08-Jul-2025 04:22:10 Europe/Berlin] === EXPORTAR_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:22:10 Europe/Berlin] === EXPORTAR_PROSPECTOS.PHP INICIADO ===
[08-Jul-2025 04:22:11 Europe/Berlin] Columnas disponibles en tb_inteletgroup_prospectos: id, usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro, direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta, numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta, competencia_actual, tipo_persona, documentos_adjuntos, fecha_registro, fecha_actualizacion, estado
[08-Jul-2025 04:22:14 Europe/Berlin] Columnas disponibles en tb_inteletgroup_prospectos: id, usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro, direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta, numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta, competencia_actual, tipo_persona, documentos_adjuntos, fecha_registro, fecha_actualizacion, estado
